"""
Window Manager for Auto Shutdown Application
Handles creation and management of settings and shutdown overlay windows
"""

import os
import sys
import subprocess


class WindowManager:
    def __init__(self):
        """Initialize the window manager"""
        self.base_path = os.path.dirname(os.path.abspath(__file__))

    def show_settings_window(self):
        """Show settings window in a separate process"""
        try:
            subprocess.Popen([
                sys.executable,
                'settings_window.py'
            ], cwd=self.base_path)
            print("Settings window opened")
        except Exception as e:
            print(f"Failed to open settings window: {e}")
            raise

    def show_shutdown_overlay(self):
        """Show shutdown overlay window in a separate process"""
        try:
            subprocess.Popen([
                sys.executable,
                'shutdown_overlay_window.py'
            ], cwd=self.base_path)
            print("Shutdown overlay opened")
        except Exception as e:
            print(f"Failed to show shutdown overlay: {e}")
            raise

    def get_base_path(self):
        """Get the base path for the application"""
        return self.base_path
