"""
Shutdown Overlay Window for Auto Shutdown Application
Runs as a separate process to show the shutdown warning overlay
"""

import webview
import sys
import os
import threading
import time
import subprocess
from config_manager import ConfigManager


class ShutdownOverlayAPI:
    def __init__(self):
        self.config_manager = ConfigManager()
        config = self.config_manager.load_config()
        self.countdown_duration = config.get('settings', {}).get('countdown_duration', 30)
        self.countdown_thread = None
        self.is_active = True
        
        # Start countdown immediately
        self.start_countdown()
    
    def get_countdown_duration(self):
        """Get countdown duration for JavaScript"""
        return self.countdown_duration
    
    def cancel_shutdown(self):
        """Cancel shutdown (called from JavaScript)"""
        print("Shutdown cancelled by user")
        self.is_active = False
        # Close the window
        try:
            webview.windows[0].destroy()
        except:
            pass
        return {"success": True}
    
    def start_countdown(self):
        """Start the countdown timer"""
        def countdown():
            for remaining in range(self.countdown_duration, 0, -1):
                if not self.is_active:
                    return
                
                # Update countdown display
                try:
                    webview.windows[0].evaluate_js(f'updateCountdown({remaining})')
                except:
                    pass
                
                time.sleep(1)
            
            # Time's up - execute shutdown
            if self.is_active:
                self.execute_shutdown()
        
        self.countdown_thread = threading.Thread(target=countdown, daemon=True)
        self.countdown_thread.start()
    
    def execute_shutdown(self):
        """Execute system shutdown"""
        print("Executing shutdown...")
        try:
            if sys.platform == "win32":
                subprocess.run(['shutdown', '/s', '/f', '/t', '0'], check=True)
            elif sys.platform in ["linux", "darwin"]:
                subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
            else:
                print(f"Unsupported platform: {sys.platform}")
        except subprocess.CalledProcessError as e:
            print(f"Failed to shutdown: {e}")
        except FileNotFoundError:
            print("Shutdown command not found - running in test mode")


def main():
    """Run the shutdown overlay window"""
    try:
        # Create API instance
        api = ShutdownOverlayAPI()
        
        # Create fullscreen overlay window
        window = webview.create_window(
            'Shutdown Warning',
            'static/shutdown_overlay.html',
            fullscreen=True,
            on_top=True,
            shadow=False,
            resizable=False,
            js_api=api
        )
        
        # Start webview
        webview.start(debug=False)
        
    except Exception as e:
        print(f"Shutdown overlay error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
