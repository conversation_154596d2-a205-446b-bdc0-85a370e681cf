"""
System Tray Manager for Auto Shutdown Application
Handles system tray icon creation and menu management
"""

import os
import sys
import subprocess
import pystray
from PIL import Image, ImageDraw


class TrayManager:
    def __init__(self, config_manager, window_manager, scheduler_manager):
        """
        Initialize the tray manager
        
        Args:
            config_manager: ConfigManager instance
            window_manager: WindowManager instance  
            scheduler_manager: SchedulerManager instance
        """
        self.config_manager = config_manager
        self.window_manager = window_manager
        self.scheduler_manager = scheduler_manager
        self.tray_icon = None
        self.is_running = True

    def create_tray_icon(self):
        """Create system tray icon with menu"""
        # Create a sophisticated power icon
        image = Image.new('RGBA', (64, 64), color=(0, 0, 0, 0))
        draw = ImageDraw.Draw(image)

        # Draw power symbol
        draw.ellipse([8, 8, 56, 56], outline='#667eea', width=4)
        draw.line([32, 16, 32, 32], fill='#667eea', width=4)

        # Create menu with all options
        menu = pystray.Menu(
            pystray.MenuItem("Settings", self.show_settings),
            pystray.MenuItem("View Schedules", self.show_schedules_info),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Test Shutdown", self.test_shutdown),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Exit", self.quit_app)
        )

        self.tray_icon = pystray.Icon("AutoShutdown", image, "Auto Shutdown", menu)
        return self.tray_icon

    def show_settings(self, icon=None, item=None):
        """Show settings window"""
        try:
            self.window_manager.show_settings_window()
        except Exception as e:
            print(f"Failed to open settings window: {e}")

    def show_schedules_info(self, icon=None, item=None):
        """Show quick info about active schedules"""
        config = self.config_manager.load_config()
        schedules = config.get('schedules', [])
        active_schedules = [s for s in schedules if s.get('enabled', True)]

        if not active_schedules:
            message = "No active schedules configured."
        else:
            message = f"Active schedules ({len(active_schedules)}):\n"
            for schedule in active_schedules[:3]:  # Show max 3
                days = ', '.join(schedule.get('days', []))
                message += f"• {schedule.get('name', 'Unnamed')} - {schedule.get('time', 'N/A')} ({days})\n"
            if len(active_schedules) > 3:
                message += f"... and {len(active_schedules) - 3} more"

        # For now, print to console. In a real app, you might show a notification
        print(message)

    def test_shutdown(self, icon=None, item=None):
        """Test the shutdown overlay (for debugging)"""
        try:
            self.window_manager.show_shutdown_overlay()
        except Exception as e:
            print(f"Failed to show shutdown overlay: {e}")

    def quit_app(self, icon=None, item=None):
        """Quit the application"""
        self.is_running = False
        if self.scheduler_manager:
            self.scheduler_manager.stop()
        if self.tray_icon:
            self.tray_icon.stop()
        sys.exit(0)

    def run(self):
        """Run the system tray"""
        if self.tray_icon:
            self.tray_icon.run()

    def stop(self):
        """Stop the system tray"""
        self.is_running = False
        if self.tray_icon:
            self.tray_icon.stop()
