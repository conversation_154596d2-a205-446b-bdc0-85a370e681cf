# Auto Shutdown Application

A beautiful automatic shutdown application built with pywebview featuring an iPhone-style slide-to-unlock interface and comprehensive system tray integration.

## Features

### 🎯 Core Features
- **System Tray Integration**: Runs as a background application with system tray icon
- **Scheduled Shutdown Management**: Configure shutdown schedules for specific days and times
- **Multiple Time Slots**: Support for multiple shutdown schedules per day/week
- **iPhone-style Cancellation**: Fullscreen overlay with slide-to-unlock interface to cancel shutdowns
- **30-Second Countdown**: Automatic shutdown after 30 seconds unless cancelled

### 🎨 User Interface
- **Modern Web-based Settings**: Beautiful HTML/CSS/JS interface using pywebview
- **Intuitive Day Selector**: Easy-to-use day-of-week picker with time range selection
- **Visual Feedback**: Clean, modern design with smooth animations
- **Responsive Design**: Works well on different screen sizes

### ⚙️ Technical Features
- **Persistent Configuration**: Settings stored in JSON configuration file
- **Windows Integration**: Proper Windows shutdown commands
- **Error Handling**: Comprehensive error handling and user notifications
- **Background Operation**: Efficient background scheduling with APScheduler

## Installation

### Prerequisites
- Python 3.7 or higher
- Windows OS (for shutdown functionality)

### Install Dependencies
```bash
pip install -r requirements.txt
```

### Required Packages
- `pywebview>=4.4.1` - Web-based GUI framework
- `pystray>=0.19.4` - System tray integration
- `Pillow>=10.0.0` - Image processing for tray icon
- `apscheduler>=3.10.4` - Advanced scheduling functionality
- `psutil>=5.9.0` - System utilities

## Quick Start

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the test suite** (optional):
   ```bash
   python test_app.py
   ```

3. **Start the application**:
   ```bash
   python main.py
   ```

4. **Look for the system tray icon** (power symbol) in your system tray

5. **Right-click the tray icon** to access:
   - Settings
   - View Schedules
   - Test Shutdown (for testing the overlay)
   - Exit

## Usage

### Setting Up Schedules

1. **Right-click the system tray icon** and select "Settings"
2. **Click "Add Schedule"** to create a new shutdown schedule
3. **Configure the schedule**:
   - Enter a descriptive name (e.g., "Weekday Evening")
   - Set the shutdown time (24-hour format)
   - Select the days of the week
   - Enable/disable the schedule
4. **Click "Save"** to add the schedule
5. **Click "Save Settings"** to apply all changes

### Managing Schedules

- **Edit**: Click the "Edit" button on any schedule to modify it
- **Delete**: Click the "Delete" button to remove a schedule
- **Enable/Disable**: Use the checkbox when creating/editing schedules

### General Settings

- **Countdown Duration**: Set how long the cancellation overlay appears (10-300 seconds)
- **Enable Notifications**: Toggle system notifications (future feature)
- **Start with Windows**: Automatically start the application with Windows (future feature)

### Cancelling a Shutdown

When a scheduled shutdown is triggered:

1. **Fullscreen overlay appears** with a countdown timer
2. **Slide the button to the right** to cancel the shutdown
3. **Must slide at least 80%** of the track width to cancel
4. **Visual feedback** shows progress with color changes and glow effects
5. **Automatic shutdown** occurs if no action is taken within the countdown period

## Code Structure

The application has been refactored into modular components for better maintainability:

### Core Components

- **`main.py`** - Main application entry point and orchestration
- **`config_manager.py`** - Configuration file management
- **`tray_manager.py`** - System tray icon and menu management
- **`scheduler_manager.py`** - Shutdown scheduling and execution
- **`window_manager.py`** - Window creation and process management

### Window Components

- **`settings_window.py`** - Settings interface (runs as separate process)
- **`shutdown_overlay_window.py`** - Shutdown warning overlay (runs as separate process)

### Static Assets

- **`static/settings.html`** - Settings interface with inline CSS
- **`static/settings.js`** - Settings interface JavaScript
- **`static/shutdown_overlay.html`** - Shutdown overlay with inline CSS
- **`static/shutdown_overlay.js`** - Shutdown overlay JavaScript

### Key Design Decisions

1. **Modular Architecture**: Each component has a single responsibility
2. **Process Separation**: Windows run in separate processes to avoid threading issues
3. **Inline CSS**: All styles are embedded in HTML files for self-contained components
4. **Manager Pattern**: Each manager handles a specific aspect of the application

## File Structure

```
auto-shutdown/
├── main.py                      # Main application entry point
├── config_manager.py            # Configuration management
├── tray_manager.py              # System tray management
├── scheduler_manager.py         # Shutdown scheduling
├── window_manager.py            # Window process management
├── settings_window.py           # Settings interface process
├── shutdown_overlay_window.py   # Shutdown overlay process
├── test_app.py                  # Test suite
├── requirements.txt             # Python dependencies
├── README.md                    # This file
├── config.json                  # User configuration (created automatically)
└── static/                      # Web interface files
    ├── settings.html            # Settings page (with inline CSS)
    ├── settings.js              # Settings JavaScript
    ├── shutdown_overlay.html    # Shutdown overlay (with inline CSS)
    └── shutdown_overlay.js      # Overlay JavaScript
```

## Configuration File

The application stores settings in `config.json`:

```json
{
  "schedules": [
    {
      "id": 1,
      "name": "Weekday Evening",
      "time": "22:00",
      "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
      "enabled": true
    }
  ],
  "settings": {
    "countdown_duration": 30,
    "enable_notifications": true,
    "auto_start": false
  }
}
```

## Testing

Run the test suite to verify functionality:

```bash
python test_app.py
```

This will:
- Test configuration management
- Test schedule creation and validation
- Create a sample configuration file
- Verify all components work together

## Troubleshooting

### Common Issues

1. **Application doesn't start**:
   - Check that all dependencies are installed
   - Verify Python version (3.7+)
   - Run `python test_app.py` to check for errors

2. **System tray icon not visible**:
   - Check Windows system tray settings
   - Look in the hidden icons area
   - Restart the application

3. **Shutdown doesn't work**:
   - Ensure you're running on Windows
   - Check that the application has necessary permissions
   - Test with "Test Shutdown" from the tray menu

4. **Settings window doesn't open**:
   - Check for firewall blocking local connections
   - Verify pywebview installation
   - Check console output for errors

### Debug Mode

For debugging, you can modify the webview.start() calls in the code to include `debug=True`.

## Future Enhancements

- [ ] Windows startup integration
- [ ] System notifications
- [ ] Multiple countdown durations per schedule
- [ ] Hibernate/sleep options
- [ ] Network-based remote shutdown
- [ ] Schedule import/export
- [ ] Dark/light theme toggle

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.

## License

This project is open source and available under the MIT License.
