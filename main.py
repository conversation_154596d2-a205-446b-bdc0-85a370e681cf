"""
Auto Shutdown Application
A beautiful system tray application for scheduled shutdowns with slide-to-cancel interface
"""

import threading
import time

from config_manager import Config<PERSON>anager
from tray_manager import Tray<PERSON>anager
from scheduler_manager import SchedulerManager
from window_manager import WindowManager


class AutoShutdownApp:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.is_running = True

        # Initialize managers
        self.window_manager = WindowManager()
        self.scheduler_manager = SchedulerManager(self.config_manager, self.window_manager)
        self.tray_manager = TrayManager(self.config_manager, self.window_manager, self.scheduler_manager)

        # Setup scheduled shutdowns
        self.scheduler_manager.setup_scheduled_shutdowns()

    def run(self):
        """Run the application"""
        # Create and run tray icon in separate thread
        tray_thread = threading.Thread(target=self.run_tray, daemon=True)
        tray_thread.start()

        # Keep main thread alive
        try:
            while self.is_running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.quit_app()

    def run_tray(self):
        """Run system tray"""
        icon = self.tray_manager.create_tray_icon()
        icon.run()

    def quit_app(self):
        """Quit the application"""
        self.is_running = False
        self.tray_manager.quit_app()


if __name__ == "__main__":
    app = AutoShutdownApp()
    app.run()
