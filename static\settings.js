// Settings JavaScript for Auto Shutdown Application

class SettingsManager {
    constructor() {
        this.config = null;
        this.currentEditingSchedule = null;
        this.init();
    }

    async init() {
        await this.loadConfig();
        this.setupEventListeners();
        this.renderSchedules();
        this.loadSettings();
    }

    async loadConfig() {
        try {
            this.config = await pywebview.api.get_config();
        } catch (error) {
            console.error('Failed to load config:', error);
            this.config = { schedules: [], settings: {} };
        }
    }

    setupEventListeners() {
        // Add schedule button
        document.getElementById('add-schedule-btn').addEventListener('click', () => {
            this.showScheduleModal();
        });

        // Save settings button
        document.getElementById('save-btn').addEventListener('click', () => {
            this.saveSettings();
        });

        // Cancel button
        document.getElementById('cancel-btn').addEventListener('click', () => {
            window.close();
        });

        // Modal event listeners
        this.setupModalEventListeners();
    }

    setupModalEventListeners() {
        const modal = document.getElementById('schedule-modal');
        const closeBtn = modal.querySelector('.close');
        const saveBtn = document.getElementById('save-schedule-btn');
        const cancelBtn = document.getElementById('cancel-schedule-btn');

        closeBtn.addEventListener('click', () => {
            this.hideScheduleModal();
        });

        cancelBtn.addEventListener('click', () => {
            this.hideScheduleModal();
        });

        saveBtn.addEventListener('click', () => {
            this.saveSchedule();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                this.hideScheduleModal();
            }
        });
    }

    renderSchedules() {
        const schedulesList = document.getElementById('schedules-list');
        schedulesList.innerHTML = '';

        if (!this.config.schedules || this.config.schedules.length === 0) {
            schedulesList.innerHTML = '<p class="no-schedules">No schedules configured</p>';
            return;
        }

        this.config.schedules.forEach(schedule => {
            const scheduleElement = this.createScheduleElement(schedule);
            schedulesList.appendChild(scheduleElement);
        });
    }

    createScheduleElement(schedule) {
        const div = document.createElement('div');
        div.className = 'schedule-item';
        div.innerHTML = `
            <div class="schedule-info">
                <h3>${schedule.name || 'Unnamed Schedule'}</h3>
                <p>Time: ${schedule.time}</p>
                <p>Days: ${schedule.days ? schedule.days.join(', ') : 'None'}</p>
                <p class="schedule-status ${schedule.enabled ? 'enabled' : 'disabled'}">
                    ${schedule.enabled ? 'Enabled' : 'Disabled'}
                </p>
            </div>
            <div class="schedule-actions">
                <button class="btn btn-small btn-secondary edit-btn" data-id="${schedule.id}">Edit</button>
                <button class="btn btn-small btn-danger delete-btn" data-id="${schedule.id}">Delete</button>
            </div>
        `;

        // Add event listeners
        div.querySelector('.edit-btn').addEventListener('click', () => {
            this.editSchedule(schedule);
        });

        div.querySelector('.delete-btn').addEventListener('click', () => {
            this.deleteSchedule(schedule.id);
        });

        return div;
    }

    showScheduleModal(schedule = null) {
        const modal = document.getElementById('schedule-modal');
        const title = document.getElementById('modal-title');
        
        this.currentEditingSchedule = schedule;
        
        if (schedule) {
            title.textContent = 'Edit Schedule';
            this.populateScheduleForm(schedule);
        } else {
            title.textContent = 'Add Schedule';
            this.clearScheduleForm();
        }
        
        modal.style.display = 'block';
    }

    hideScheduleModal() {
        const modal = document.getElementById('schedule-modal');
        modal.style.display = 'none';
        this.currentEditingSchedule = null;
    }

    populateScheduleForm(schedule) {
        document.getElementById('schedule-name').value = schedule.name || '';
        document.getElementById('schedule-time').value = schedule.time || '22:00';
        document.getElementById('schedule-enabled').checked = schedule.enabled !== false;

        // Clear all day checkboxes first
        const dayCheckboxes = document.querySelectorAll('.days-selector input[type="checkbox"]');
        dayCheckboxes.forEach(cb => cb.checked = false);

        // Check the days for this schedule
        if (schedule.days) {
            schedule.days.forEach(day => {
                const checkbox = document.querySelector(`.days-selector input[value="${day.toLowerCase()}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        }
    }

    clearScheduleForm() {
        document.getElementById('schedule-name').value = '';
        document.getElementById('schedule-time').value = '22:00';
        document.getElementById('schedule-enabled').checked = true;
        
        const dayCheckboxes = document.querySelectorAll('.days-selector input[type="checkbox"]');
        dayCheckboxes.forEach(cb => cb.checked = false);
    }

    saveSchedule() {
        const name = document.getElementById('schedule-name').value.trim();
        const time = document.getElementById('schedule-time').value;
        const enabled = document.getElementById('schedule-enabled').checked;
        
        const selectedDays = Array.from(document.querySelectorAll('.days-selector input[type="checkbox"]:checked'))
            .map(cb => cb.value);

        if (!name) {
            alert('Please enter a schedule name');
            return;
        }

        if (selectedDays.length === 0) {
            alert('Please select at least one day');
            return;
        }

        const scheduleData = {
            name,
            time,
            days: selectedDays,
            enabled
        };

        if (this.currentEditingSchedule) {
            // Update existing schedule
            scheduleData.id = this.currentEditingSchedule.id;
            const index = this.config.schedules.findIndex(s => s.id === this.currentEditingSchedule.id);
            if (index !== -1) {
                this.config.schedules[index] = scheduleData;
            }
        } else {
            // Add new schedule
            scheduleData.id = Date.now(); // Simple ID generation
            this.config.schedules.push(scheduleData);
        }

        this.renderSchedules();
        this.hideScheduleModal();
    }

    editSchedule(schedule) {
        this.showScheduleModal(schedule);
    }

    deleteSchedule(scheduleId) {
        if (confirm('Are you sure you want to delete this schedule?')) {
            this.config.schedules = this.config.schedules.filter(s => s.id !== scheduleId);
            this.renderSchedules();
        }
    }

    loadSettings() {
        const settings = this.config.settings || {};
        
        document.getElementById('countdown-duration').value = settings.countdown_duration || 30;
        document.getElementById('enable-notifications').checked = settings.enable_notifications !== false;
        document.getElementById('auto-start').checked = settings.auto_start === true;
    }

    async saveSettings() {
        // Update settings from form
        this.config.settings = {
            countdown_duration: parseInt(document.getElementById('countdown-duration').value),
            enable_notifications: document.getElementById('enable-notifications').checked,
            auto_start: document.getElementById('auto-start').checked
        };

        try {
            const result = await pywebview.api.save_config(this.config);
            if (result.success) {
                alert('Settings saved successfully!');
            } else {
                alert('Failed to save settings');
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            alert('Failed to save settings');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SettingsManager();
});
