"""
Configuration Manager for Auto Shutdown Application
Handles loading and saving of user settings and schedules
"""

import json
import os
from pathlib import Path
from typing import Dict, Any


class ConfigManager:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.default_config = {
            "schedules": [],
            "settings": {
                "countdown_duration": 30,
                "enable_notifications": True,
                "auto_start": False
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        if not self.config_file.exists():
            return self.default_config.copy()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # Merge with default config to ensure all keys exist
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading config: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except IOError as e:
            print(f"Error saving config: {e}")
            return False
    
    def add_schedule(self, schedule: Dict[str, Any]) -> bool:
        """Add a new schedule"""
        config = self.load_config()
        
        # Generate unique ID if not provided
        if 'id' not in schedule:
            existing_ids = [s.get('id', 0) for s in config['schedules']]
            schedule['id'] = max(existing_ids, default=0) + 1
        
        config['schedules'].append(schedule)
        return self.save_config(config)
    
    def remove_schedule(self, schedule_id: int) -> bool:
        """Remove a schedule by ID"""
        config = self.load_config()
        config['schedules'] = [s for s in config['schedules'] if s.get('id') != schedule_id]
        return self.save_config(config)
    
    def update_schedule(self, schedule_id: int, updated_schedule: Dict[str, Any]) -> bool:
        """Update an existing schedule"""
        config = self.load_config()
        
        for i, schedule in enumerate(config['schedules']):
            if schedule.get('id') == schedule_id:
                updated_schedule['id'] = schedule_id
                config['schedules'][i] = updated_schedule
                return self.save_config(config)
        
        return False
    
    def get_schedules(self) -> list:
        """Get all schedules"""
        config = self.load_config()
        return config.get('schedules', [])
    
    def update_settings(self, settings: Dict[str, Any]) -> bool:
        """Update application settings"""
        config = self.load_config()
        config['settings'].update(settings)
        return self.save_config(config)
