"""
Settings Window for Auto Shutdown Application
Runs as a separate process to avoid threading issues with pywebview
"""

import webview
import sys
import os
from config_manager import ConfigManager


class SettingsAPI:
    def __init__(self):
        self.config_manager = ConfigManager()
    
    def get_config(self):
        """Get current configuration for web interface"""
        return self.config_manager.load_config()
    
    def save_config(self, new_config):
        """Save configuration from web interface"""
        success = self.config_manager.save_config(new_config)
        return {"success": success}


def main():
    """Run the settings window"""
    try:
        # Create API instance
        api = SettingsAPI()
        
        # Create settings window
        window = webview.create_window(
            'Auto Shutdown Settings',
            'static/settings.html',
            width=800,
            height=600,
            resizable=True,
            js_api=api
        )
        
        # Start webview
        webview.start(debug=False)
        
    except Exception as e:
        print(f"Settings window error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
